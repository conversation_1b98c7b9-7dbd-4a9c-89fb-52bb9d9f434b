import React from 'react';
import { Phone, Mail, MapPin, ArrowUp } from 'lucide-react';

const Footer: React.FC = () => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-12 grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="text-3xl font-bold gradient-nova-text mb-4">
              Novalead
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed max-w-md">
              Professional telemarketing and lead generation services that drive business growth.
              We help companies build stronger customer relationships and increase revenue through
              strategic communication and expert outreach.
            </p>
            <div className="space-y-3">
              <div className="flex items-center text-gray-300">
                <Phone size={18} className="mr-3 text-nova-red" />
                <span>+****************</span>
              </div>
              <div className="flex items-center text-gray-300">
                <Mail size={18} className="mr-3 text-nova-blue" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center text-gray-300">
                <MapPin size={18} className="mr-3 text-nova-orange" />
                <span>123 Business Ave, Suite 100, New York, NY 10001</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <button
                  onClick={() => scrollToSection('home')}
                  className="text-gray-300 hover:text-nova-red transition-colors duration-200"
                >
                  Home
                </button>
              </li>
              <li>
                <button
                  onClick={() => scrollToSection('services')}
                  className="text-gray-300 hover:text-nova-red transition-colors duration-200"
                >
                  Services
                </button>
              </li>
              <li>
                <button
                  onClick={() => scrollToSection('contact')}
                  className="text-gray-300 hover:text-nova-red transition-colors duration-200"
                >
                  Contact
                </button>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-nova-red transition-colors duration-200">
                  About Us
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-nova-red transition-colors duration-200">
                  Case Studies
                </a>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Our Services</h3>
            <ul className="space-y-2 text-sm">
              <li className="text-gray-300">Lead Generation</li>
              <li className="text-gray-300">Cross-selling & Upselling</li>
              <li className="text-gray-300">Appointment Scheduling</li>
              <li className="text-gray-300">Retention Calls</li>
              <li className="text-gray-300">Inbound Telemarketing</li>
              <li className="text-gray-300">Customer Research</li>
              <li className="text-gray-300">Calendar Management</li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 py-6 flex flex-col md:flex-row justify-between items-center">
          <div className="text-gray-400 text-sm mb-4 md:mb-0">
            © 2024 Novalead. All rights reserved.
          </div>

          <div className="flex items-center space-x-6">
            <a href="#" className="text-gray-400 hover:text-nova-red transition-colors duration-200 text-sm">
              Privacy Policy
            </a>
            <a href="#" className="text-gray-400 hover:text-nova-red transition-colors duration-200 text-sm">
              Terms of Service
            </a>
            <button
              onClick={scrollToTop}
              className="bg-nova-red text-white p-2 rounded-lg hover:bg-opacity-90 transition-all duration-200 group"
              aria-label="Scroll to top"
            >
              <ArrowUp size={16} className="group-hover:-translate-y-1 transition-transform duration-200" />
            </button>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
