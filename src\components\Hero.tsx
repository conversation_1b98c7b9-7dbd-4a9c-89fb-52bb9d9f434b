import React from 'react';
import { ArrowRight, Phone, Users, TrendingUp } from 'lucide-react';

const Hero: React.FC = () => {
  const scrollToServices = () => {
    const element = document.getElementById('services');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const scrollToContact = () => {
    const element = document.getElementById('contact');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="pt-20 pb-16 bg-gradient-to-br from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Content */}
          <div className="animate-fade-in-up">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
              Professional{' '}
              <span className="gradient-nova-text">
                Telemarketing
              </span>{' '}
              & Lead Generation Services
            </h1>

            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Transform your sales pipeline with Novalead's expert telemarketing services.
              We deliver high-quality leads, enhance customer relationships, and drive revenue growth
              through professional outreach and strategic communication.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 mb-12">
              <button
                onClick={scrollToContact}
                className="bg-nova-red text-white px-8 py-4 rounded-lg hover:bg-opacity-90 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl flex items-center justify-center group"
              >
                Get Started Today
                <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform duration-200" size={20} />
              </button>

              <button
                onClick={scrollToServices}
                className="border-2 border-nova-red text-nova-red px-8 py-4 rounded-lg hover:bg-nova-red hover:text-white transition-all duration-200 font-semibold"
              >
                View Our Services
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-nova-red mb-2">500+</div>
                <div className="text-gray-600 text-sm">Satisfied Clients</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-nova-orange mb-2">95%</div>
                <div className="text-gray-600 text-sm">Success Rate</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-nova-blue mb-2">24/7</div>
                <div className="text-gray-600 text-sm">Support</div>
              </div>
            </div>
          </div>

          {/* Right Column - Visual Elements */}
          <div className="relative">
            <div className="gradient-nova-subtle rounded-2xl p-8 backdrop-blur-sm">
              <div className="grid grid-cols-2 gap-6">
                {/* Service Icons */}
                <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
                  <Phone className="text-nova-red mb-4" size={32} />
                  <h3 className="font-semibold text-gray-900 mb-2">Expert Calling</h3>
                  <p className="text-gray-600 text-sm">Professional outreach that converts</p>
                </div>

                <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
                  <Users className="text-nova-blue mb-4" size={32} />
                  <h3 className="font-semibold text-gray-900 mb-2">Lead Qualification</h3>
                  <p className="text-gray-600 text-sm">Quality prospects for your team</p>
                </div>

                <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
                  <TrendingUp className="text-nova-orange mb-4" size={32} />
                  <h3 className="font-semibold text-gray-900 mb-2">Revenue Growth</h3>
                  <p className="text-gray-600 text-sm">Measurable business results</p>
                </div>

                <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
                  <div className="w-8 h-8 bg-nova-purple rounded-lg flex items-center justify-center mb-4">
                    <span className="text-white font-bold text-sm">24</span>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">24/7 Service</h3>
                  <p className="text-gray-600 text-sm">Round-the-clock support</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
